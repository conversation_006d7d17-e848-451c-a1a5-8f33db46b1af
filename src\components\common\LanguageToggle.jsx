import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { BiGlobe } from "react-icons/bi";

const LanguageToggle = () => {
  const { i18n } = useTranslation();

  const toggleLanguage = () => {
    const newLang = i18n.language === "ar" ? "en" : "ar";
    i18n.changeLanguage(newLang);

    // Update document direction for RTL support
    document.documentElement.dir = newLang === "ar" ? "rtl" : "ltr";
    document.documentElement.lang = newLang;

    // Save language preference
    localStorage.setItem("language", newLang);
  };

  useEffect(() => {
    // Set initial direction and language
    const currentLang = i18n.language;
    document.documentElement.dir = currentLang === "ar" ? "rtl" : "ltr";
    document.documentElement.lang = currentLang;

    // Add RTL class to body for additional styling
    if (currentLang === "ar") {
      document.body.classList.add("rtl");
    } else {
      document.body.classList.remove("rtl");
    }
  }, [i18n.language]);

  return (
    <div
      className="bg-white shadow-md icon-box dark:bg-dark-light hover:shadow-lg hover:bg-transparent cursor-pointer flex items-center gap-1"
      onClick={toggleLanguage}
      title={
        i18n.language === "ar" ? "Switch to English" : "التبديل إلى العربية"
      }
    >
      <BiGlobe className="text-lg" />
      <span className="text-sm font-medium">
        {i18n.language === "ar" ? "EN" : "ع"}
      </span>
    </div>
  );
};

export default LanguageToggle;
