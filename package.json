{"name": "react-base-project", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.13", "framer-motion": "^7.6.19", "material-react-table": "^1.5.4", "postcss": "^8.4.19", "postcss-cli": "^10.1.0", "react": "^18.2.0", "react-calendar": "^4.0.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-icons": "^4.6.0", "react-paginate": "^8.1.4", "react-rating-stars-component": "^2.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.4.2", "react-scripts": "5.0.1", "tailwindcss": "^3.2.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}